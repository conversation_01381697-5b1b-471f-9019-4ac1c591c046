[package]
name = "auto-live-server"
version.workspace = true
edition.workspace = true

[[bin]]
name = "auto-live-server"
path = "src/main.rs"

[dependencies]
auto-live-common = { workspace = true }
tokio = { workspace = true, features = ["full"] }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true, features = ["raw_value"] }
chrono = { workspace = true, features = ["serde"] }
anyhow = { workspace = true }
ciborium = { workspace = true }
iroh = { workspace = true }
axum = { workspace = true, features = ["ws"] }
axum-extra = { workspace = true, features = ["typed-header"] }
once_cell = { workspace = true }
itertools = { workspace = true }
http = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter", "registry"] }
sea-orm = { workspace = true, features = ["runtime-tokio-rustls", "sqlx-postgres"] }
tower = { workspace = true, features = ["full"] }
tower-http = { workspace = true, features = ["full"] }
blake2 = { workspace = true }
hex = { workspace = true }
futures-util = { workspace = true }
headers = { workspace = true }
tokio-stream = { workspace = true }
futures = { workspace = true }