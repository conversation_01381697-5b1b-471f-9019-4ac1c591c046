[package]
name = "auto-live-core"
version.workspace = true
edition.workspace = true

[dependencies]
auto-live-common = { workspace = true }
reqwest = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
chrono = { workspace = true }
tokio = { workspace = true, features = ["full"] }
anyhow = { workspace = true }
env_logger = { workspace = true }
log = { workspace = true }
symphonia = { workspace = true, features = ["mp3"] }
symphonia-core = { workspace = true }
uuid = { workspace = true, features = ["v4", "serde"] }
rodio = { workspace = true }
arc-swap = { workspace = true }
derive_more = { workspace = true, features = ["deref"] }
rand = { workspace = true }
once_cell = { workspace = true }
built = { workspace = true, features = ["chrono"] }
futures = { workspace = true }
iroh = { workspace = true, features = ["discovery-pkarr-dht", "discovery-local-network"] }
iroh-blobs = { workspace = true }
n0-future = { workspace = true }
tarpc = { workspace = true, features = ["full"] }
wry = "0.51"
winit = "0.30"
rquickjs="0.9"
interprocess = { version = "2.2", optional = true }

[build-dependencies]
built = { workspace = true, features = ["chrono"] }