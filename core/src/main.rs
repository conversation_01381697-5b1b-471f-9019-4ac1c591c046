mod configuration;
mod core;
mod infer;
mod traits;

use crate::configuration::{Configuration, <PERSON><PERSON><PERSON>, OutputDevice, SolutionSet};
use std::collections::VecDeque;

use chrono::Local;
use std::time::Duration;
use tokio::time::sleep;
use winit::application::ApplicationHandler;
use winit::event::WindowEvent;
use winit::event_loop::{ActiveEventLoop, EventLoop};
use winit::window::{Window, WindowId};
use wry::WebViewBuilder;

// Use of a mod or pub mod is not actually necessary.
pub mod built_info {
    // The file has been placed there by the build script.
    include!(concat!(env!("OUT_DIR"), "/built.rs"));
}

#[tokio::main]
async fn main() {
    #[cfg(debug_assertions)]
    {
        let build_time = built::util::strptime(built_info::BUILT_TIME_UTC)
            .with_timezone(&built::chrono::offset::Local);
        if build_time.timestamp() + 60 * 60 * 24 * 14 < Local::now().timestamp() {
            panic!("debug版本构建时间超过14天，请立刻删除并重新构建或使用正式版");
        }
    }

    





    {
        #[derive(Default)]
        struct App {
            window: Option<Window>,
            webview: Option<wry::WebView>,
        }

        impl ApplicationHandler for App {
            fn resumed(&mut self, event_loop: &ActiveEventLoop) {
                let window = event_loop.create_window(Window::default_attributes()).unwrap();
                let webview = WebViewBuilder::new()
                    .with_url("https://tauri.app")
                    .build(&window)
                    .unwrap();

                window.set_visible(false);

                self.window = Some(window);
                self.webview = Some(webview);
            }

            fn window_event(&mut self, _event_loop: &ActiveEventLoop, _window_id: WindowId, event: WindowEvent) {}
        }

        let event_loop = EventLoop::new().unwrap();
        let mut app = App::default();
        event_loop.run_app(&mut app).unwrap();


    }





    let json = include_str!("../huashu.json");

    let v = serde_json::from_str::<VecDeque<Huashu>>(&json).unwrap();

    env_logger::init();

    let core = core::Core::build(Configuration {
        output_device: OutputDevice {
            name: None,
            id: None,
        },
        solutions: SolutionSet::from([("main".to_string(), v)]),
        play_solution: "main".to_string(),
        random_play: false,
    })
    .await
    .unwrap();

    sleep(Duration::from_secs(180000000)).await;
}
