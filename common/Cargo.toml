[package]
name = "auto-live-common"
version.workspace = true
edition.workspace = true

[dependencies]
serde = { workspace = true, features = ["derive"] }
anyhow = { workspace = true }
ciborium = { workspace = true }
ciborium-io = { workspace = true }
tarpc = { workspace = true, features = ["full"] }
symphonia = { workspace = true, features = ["mp3"] }
symphonia-core = { workspace = true }
iroh = { workspace = true }
n0-future = { workspace = true }
futures-core = { workspace = true }
futures-sink = { workspace = true }
derive_more = { workspace = true, features = ["display"] }
futures = { workspace = true }
tokio-serde = { workspace = true }
tokio-util = { workspace = true }
pin-project = { workspace = true }
futures-lite = { workspace = true }
async-stream = { workspace = true }
tokio = { workspace = true }
async-trait = "0.1"
iroh-blobs = { workspace = true }
dirs = { workspace = true, optional = true }


[target.'cfg(any(target_os(windows),target_os(linux),target_os(macos)))'.dependencies]