use std::sync::{<PERSON>, Mutex};

use futures::StreamExt as _;
use iroh::protocol::ProtocolHandler;

use iroh::{Endpoint, NodeAddr};
use std::fmt::{Debug, Formatter};
use tarpc::client;
use tarpc::context::Context;
use tarpc::server::{self, Channel};
use tarpc::tokio_serde::formats::Bincode;
use tarpc::tokio_util::codec::LengthDelimitedCodec;

use crate::service::duplex_stream::Duplex;
use crate::service::infer::{TTSClient, TssInfer, TtsOpt, WavBytes, TTS};

pub const TEXT_TO_SPEAK_ALPN: &[u8] = b"TTS_ALPN";

#[derive(Clone)]
pub struct TTSServer {
    pub tts_speaker: Arc<Mutex<dyn TssInfer>>,
}

impl TTSServer {
    pub fn new(tts: impl TssInfer + 'static) -> Self {
        Self {
            tts_speaker: Arc::new(Mutex::new(tts)),
        }
    }
}

impl TTS for TTSServer {
    async fn tts(self, context: Context, opt: TtsOpt) -> WavBytes {
        let speaker = self.tts_speaker.lock().unwrap();

        speaker.tts(opt)
    }
}

impl Debug for TTSServer {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        todo!()
    }
}

impl ProtocolHandler for TTSServer {
    fn accept(
        &self,
        conn: iroh::endpoint::Connection,
    ) -> futures_lite::future::Boxed<anyhow::Result<()>> {
        let clone = self.clone();
        Box::pin(async move {
            let connection = conn;
            let conn = connection.accept_bi().await?;
            let duplex = Duplex::new(conn.1, conn.0);

            let codec_builder = LengthDelimitedCodec::builder();
            let framed = codec_builder.new_framed(duplex);

            let server_transport = tarpc::serde_transport::new(framed, Bincode::default());
            let server = server::BaseChannel::with_defaults(server_transport);

            server
                .execute(clone.serve())
                // Handle all requests concurrently.
                .for_each(|response| async move {
                    tokio::spawn(response);
                })
                .await;

            Ok(())
        })
    }
}

pub async fn create_tts_client(
    client_endpoint: &Endpoint,
    node_addr: impl Into<NodeAddr>,
) -> anyhow::Result<TTSClient> {
    let (send_stream, recv_stream) = client_endpoint
        .connect(node_addr.into(), TEXT_TO_SPEAK_ALPN)
        .await?
        .open_bi()
        .await?;
    let duplex = Duplex::new(recv_stream, send_stream);

    // WorldClient is generated by the #[tarpc::service] attribute. It has a constructor `new`
    // that takes a config and any Transport as input.
    let codec_builder = LengthDelimitedCodec::builder();
    let framed = codec_builder.new_framed(duplex);

    let client_transport = tarpc::serde_transport::new(framed, Bincode::default());
    let client = TTSClient::new(client::Config::default(), client_transport).spawn();

    Ok(client)
}
