use std::path::PathBuf;

#[cfg(any())]
pub static AITUBER_PATH: Lazy<PathBuf> =
    Lazy::new(|| dirs::data_local_dir().unwrap().join(env!("CARGO_PKG_NAME")));

#[cfg(feature = "desktop")]
pub static AITUBER_DATA_PATH: Lazy<PathBuf> = Lazy::new(|| {
    dirs::data_local_dir()
        .unwrap()
        .join(env!("CARGO_PKG_NAME"))
        .join("data")
        .join("root")
});
#[cfg(feature = "desktop")]
pub static AITUBER_TMP_DATA_PATH: Lazy<PathBuf> = Lazy::new(|| {
    dirs::data_local_dir()
        .unwrap()
        .join(env!("CARGO_PKG_NAME"))
        .join("temp")
        .join("root")
});
