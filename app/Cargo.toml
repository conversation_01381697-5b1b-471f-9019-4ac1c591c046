[package]
name = "auto-live-app"
version.workspace = true
edition.workspace = true

[lints.rust]
unused_imports = "allow"
unused_variables = "allow"

[dependencies]
dioxus = { workspace = true, features = ["html", "router"] }
dioxus-html = { workspace = true, features = ["file_engine"] }
dioxus-signals = { workspace = true }
manganis = { workspace = true }
dioxus-logger = { workspace = true }
tracing = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
reqwest = { workspace = true, features = ["json"] }
uuid = { workspace = true, default-features = false, features = ["v4"] }
chrono = { workspace = true, features = ["serde"] }
opendal = { workspace = true, default-features = false }
futures = { workspace = true }
derive_more = { workspace = true, features = ["display", "from_str", "deref"] }
jwt-compact = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
once_cell = { workspace = true }
ciborium = { workspace = true }
#async-lock = "3.4"
async-once-cell = { workspace = true }
#dotenv = "0.15"
dotenv_codegen = { workspace = true }
sevenz-rust2 = { workspace = true, features = ["compress","zstd"] }
# web only
dioxus-web = { workspace = true, optional = true }
gloo-storage = { workspace = true, optional = true }
gloo-timers = { workspace = true, optional = true, features = ["futures"] }
web-sys = { workspace = true, optional = true, features = [
    "Window",
    "Navigator",
    "AudioContext",
    "AudioBufferSourceNode",
    "AudioBuffer",
    "AudioDestinationNode",
    "BeforeUnloadEvent"
] }
js-sys = { workspace = true, optional = true }
wasm-bindgen-futures = { workspace = true, optional = true }
opendal-indexeddb = { git = "https://github.com/TimJyun/opendal-indexeddb", optional = true }
# desktop only
dioxus-desktop = { workspace = true, optional = true }
# mobile only
dioxus-mobile = { workspace = true, optional = true }
# native only
tokio = { workspace = true, optional = true, features = ["time"] }
sysinfo = { workspace = true, optional = true, features = ["serde"] }
dirs = { workspace = true, optional = true }
interprocess = "2.2.3"
#sqlx = {version = "*"}

[features]
default = ["desktop"]
web = [
    "dioxus/web", "dioxus-web",
    "gloo-storage", "gloo-timers",
    "web-sys", "js-sys",
    "opendal-indexeddb",
    "uuid/js",
    "wasm-bindgen-futures"
]
desktop = [
    "dioxus/desktop",
    "dioxus-desktop",
    "native"
]
mobile = [
    "dioxus/mobile",
    "dioxus-mobile",
    "native"
]
native = [
    "opendal/services-fs",
    "tokio", "sysinfo",
    "dirs"
]


