[package]
name = "auto-live-infer"
version.workspace = true
edition.workspace = true

[dependencies]
auto-live-common = { workspace = true }
#gpt_sovits_rs = { workspace = true }
anyhow = { workspace = true }
tarpc = { workspace = true }
ciborium = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
wav_io = { workspace = true }
tokio = { workspace = true, features = ["full"] }
chrono = { workspace = true }
reqwest = { workspace = true }
env_logger = { workspace = true }
log = { workspace = true }
#tch = { version = "0.17" }
symphonia = { workspace = true, features = ["mp3"] }
symphonia-core = { workspace = true }
uuid = { workspace = true, features = ["v4", "serde"] }
rodio = { workspace = true }
derive_more = { workspace = true, features = ["deref"] }
once_cell = { workspace = true }
axum = { workspace = true }
itertools = { workspace = true }
http = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter", "registry"] }
tower = { workspace = true, features = ["full"] }
tower-http = { workspace = true, features = ["full"] }
blake2 = { workspace = true }
hex = { workspace = true }
tokio-tungstenite = { workspace = true }
tungstenite = { workspace = true }
futures = { workspace = true }
url = { workspace = true }
tokio-util = { workspace = true, features = ["io"] }
lofty = { workspace = true }
iroh = { workspace = true, features = ["discovery-pkarr-dht", "discovery-local-network"] }
iroh-blobs = { workspace = true }
n0-future = { workspace = true }
interprocess = { version = "2.2" }